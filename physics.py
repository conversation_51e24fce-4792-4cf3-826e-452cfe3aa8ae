import math
import numpy as np
from config import *

class Vector2:
    def __init__(self, x=0, y=0):
        self.x = float(x)
        self.y = float(y)
    
    def __add__(self, other):
        return Vector2(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other):
        return Vector2(self.x - other.x, self.y - other.y)
    
    def __mul__(self, scalar):
        return Vector2(self.x * scalar, self.y * scalar)
    
    def __truediv__(self, scalar):
        return Vector2(self.x / scalar, self.y / scalar)
    
    def magnitude(self):
        return math.sqrt(self.x * self.x + self.y * self.y)
    
    def normalize(self):
        mag = self.magnitude()
        if mag > 0:
            return Vector2(self.x / mag, self.y / mag)
        return Vector2(0, 0)
    
    def dot(self, other):
        return self.x * other.x + self.y * other.y
    
    def copy(self):
        return Vector2(self.x, self.y)

class SurfPhysics:
    def __init__(self):
        self.position = Vector2(100, 100)
        self.velocity = Vector2(0, 0)
        self.on_ground = False
        self.on_surf = False
        self.surf_normal = Vector2(0, -1)  # нормаль поверхности
        
    def apply_gravity(self, dt):
        """Применяет гравитацию"""
        if not self.on_ground:
            self.velocity.y += GRAVITY * dt
    
    def apply_friction(self, dt):
        """Применяет трение в зависимости от поверхности"""
        if self.on_ground:
            friction = SURF_FRICTION if self.on_surf else FRICTION
            self.velocity.x *= friction
        else:
            # В воздухе меньше трения
            self.velocity.x *= AIR_FRICTION
            self.velocity.y *= AIR_FRICTION
    
    def air_strafe(self, mouse_delta, strafe_input, dt):
        """Реализует air strafing механику"""
        if self.on_ground:
            return
            
        # Поворот направления на основе движения мыши
        turn_speed = mouse_delta * MOUSE_SENSITIVITY
        
        # Вычисляем желаемое направление движения
        if strafe_input != 0:
            # Получаем текущее направление движения
            current_speed = self.velocity.magnitude()
            if current_speed > 0:
                current_dir = self.velocity.normalize()
                
                # Поворачиваем направление
                new_angle = math.atan2(current_dir.y, current_dir.x) + turn_speed * strafe_input
                new_dir = Vector2(math.cos(new_angle), math.sin(new_angle))
                
                # Применяем ускорение в новом направлении
                accel = new_dir * AIR_ACCEL * dt * abs(strafe_input)
                self.velocity = self.velocity + accel
                
                # Ограничиваем максимальную скорость
                if self.velocity.magnitude() > MAX_SPEED:
                    self.velocity = self.velocity.normalize() * MAX_SPEED
    
    def surf_acceleration(self, dt):
        """Ускорение на surf поверхностях"""
        if not self.on_surf:
            return
            
        # Проекция скорости на поверхность
        vel_along_surface = self.velocity.dot(self.surf_normal)
        
        # Если движемся вдоль поверхности, получаем ускорение
        if abs(vel_along_surface) < 0.1:  # почти параллельно поверхности
            # Направление вниз по склону
            slope_dir = Vector2(-self.surf_normal.y, self.surf_normal.x)
            if slope_dir.x < 0:  # убеждаемся что движемся в правильном направлении
                slope_dir = slope_dir * -1
                
            # Добавляем ускорение вниз по склону
            surf_accel = slope_dir * GRAVITY * 0.3 * dt
            self.velocity = self.velocity + surf_accel
    
    def update(self, dt, mouse_delta=0, strafe_input=0):
        """Обновляет физику"""
        # Применяем гравитацию
        self.apply_gravity(dt)
        
        # Air strafing
        self.air_strafe(mouse_delta, strafe_input, dt)
        
        # Surf ускорение
        self.surf_acceleration(dt)
        
        # Применяем трение
        self.apply_friction(dt)
        
        # Обновляем позицию
        self.position = self.position + self.velocity * dt
    
    def jump(self):
        """Прыжок"""
        if self.on_ground:
            self.velocity.y = -JUMP_FORCE
            self.on_ground = False
            self.on_surf = False
