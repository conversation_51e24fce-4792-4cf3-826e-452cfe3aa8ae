import pygame
from config import *
from physics import Vector2

class Camera:
    def __init__(self):
        self.offset = Vector2(0, 0)
        self.target = None
        self.smoothing = 0.1  # скорость следования камеры
        
    def set_target(self, target):
        """Устанавливает цель для камеры"""
        self.target = target
    
    def update(self, dt):
        """Обновляет позицию камеры"""
        if self.target:
            # Целевая позиция камеры (центрируем игрока на экране)
            target_x = self.target.physics.position.x - SCREEN_WIDTH // 2
            target_y = self.target.physics.position.y - SCREEN_HEIGHT // 2
            
            # Плавное следование
            self.offset.x += (target_x - self.offset.x) * self.smoothing
            self.offset.y += (target_y - self.offset.y) * self.smoothing
    
    def world_to_screen(self, world_pos):
        """Преобразует мировые координаты в экранные"""
        return Vector2(
            world_pos.x - self.offset.x,
            world_pos.y - self.offset.y
        )
    
    def screen_to_world(self, screen_pos):
        """Преобразует экранные координаты в мировые"""
        return Vector2(
            screen_pos.x + self.offset.x,
            screen_pos.y + self.offset.y
        )
