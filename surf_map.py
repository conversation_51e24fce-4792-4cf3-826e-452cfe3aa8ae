import pygame
import math
from config import *
from physics import Vector2

class SurfRamp:
    def __init__(self, start_pos, end_pos, width=20):
        self.start_pos = Vector2(start_pos[0], start_pos[1])
        self.end_pos = Vector2(end_pos[0], end_pos[1])
        self.width = width
        
        # Вычисляем нормаль поверхности
        direction = self.end_pos - self.start_pos
        length = direction.magnitude()
        if length > 0:
            direction = direction / length
            # Нормаль перпендикулярна направлению (повернута на 90 градусов)
            self.normal = Vector2(-direction.y, direction.x)
        else:
            self.normal = Vector2(0, -1)
    
    def get_collision_rect(self):
        """Возвращает прямоугольник для проверки коллизий"""
        min_x = min(self.start_pos.x, self.end_pos.x) - self.width // 2
        max_x = max(self.start_pos.x, self.end_pos.x) + self.width // 2
        min_y = min(self.start_pos.y, self.end_pos.y) - self.width // 2
        max_y = max(self.start_pos.y, self.end_pos.y) + self.width // 2
        
        return pygame.Rect(min_x, min_y, max_x - min_x, max_y - min_y)
    
    def check_collision(self, player_rect):
        """Проверяет коллизию с игроком"""
        return self.get_collision_rect().colliderect(player_rect)
    
    def draw(self, screen, camera_offset):
        """Рисует рампу"""
        start_screen = (self.start_pos.x - camera_offset.x, self.start_pos.y - camera_offset.y)
        end_screen = (self.end_pos.x - camera_offset.x, self.end_pos.y - camera_offset.y)
        
        pygame.draw.line(screen, SURF_COLOR, start_screen, end_screen, self.width)

class Platform:
    def __init__(self, x, y, width, height, color=GRAY):
        self.rect = pygame.Rect(x, y, width, height)
        self.color = color
    
    def draw(self, screen, camera_offset):
        """Рисует платформу"""
        screen_rect = pygame.Rect(
            self.rect.x - camera_offset.x,
            self.rect.y - camera_offset.y,
            self.rect.width,
            self.rect.height
        )
        pygame.draw.rect(screen, self.color, screen_rect)

class SurfMap:
    def __init__(self):
        self.ramps = []
        self.platforms = []
        self.spawn_point = Vector2(100, 100)
        self.create_test_map()
    
    def create_test_map(self):
        """Создает тестовую карту с surf рампами"""
        # Стартовая платформа
        self.platforms.append(Platform(50, 200, 200, 20))
        
        # Первая surf рампа (вниз-вправо)
        self.ramps.append(SurfRamp((250, 220), (400, 350)))
        
        # Платформа после первой рампы
        self.platforms.append(Platform(400, 350, 100, 20))
        
        # Вторая surf рампа (вверх-вправо)
        self.ramps.append(SurfRamp((500, 370), (650, 250)))
        
        # Высокая платформа
        self.platforms.append(Platform(650, 250, 150, 20))
        
        # Длинная surf рампа для набора скорости
        self.ramps.append(SurfRamp((800, 270), (1100, 450)))
        
        # Финишная платформа
        self.platforms.append(Platform(1100, 450, 200, 20))
        
        # Дополнительные препятствия
        self.platforms.append(Platform(300, 500, 100, 20))
        self.platforms.append(Platform(600, 600, 150, 20))
        
        # Стены для ограничения карты
        self.platforms.append(Platform(0, 0, 20, 800))  # левая стена
        self.platforms.append(Platform(0, 780, 1400, 20))  # пол
        self.platforms.append(Platform(1380, 0, 20, 800))  # правая стена
        self.platforms.append(Platform(0, 0, 1400, 20))  # потолок
    
    def check_collisions(self, player_rect, player_physics):
        """Проверяет коллизии игрока с картой"""
        player_physics.on_ground = False
        player_physics.on_surf = False
        
        # Проверка коллизий с платформами
        for platform in self.platforms:
            if player_rect.colliderect(platform.rect):
                # Определяем сторону коллизии
                overlap_left = player_rect.right - platform.rect.left
                overlap_right = platform.rect.right - player_rect.left
                overlap_top = player_rect.bottom - platform.rect.top
                overlap_bottom = platform.rect.bottom - player_rect.top
                
                min_overlap = min(overlap_left, overlap_right, overlap_top, overlap_bottom)
                
                if min_overlap == overlap_top and player_physics.velocity.y > 0:
                    # Приземление сверху
                    player_physics.position.y = platform.rect.top - PLAYER_HEIGHT
                    player_physics.velocity.y = 0
                    player_physics.on_ground = True
                elif min_overlap == overlap_bottom and player_physics.velocity.y < 0:
                    # Удар головой о потолок
                    player_physics.position.y = platform.rect.bottom
                    player_physics.velocity.y = 0
                elif min_overlap == overlap_left and player_physics.velocity.x > 0:
                    # Удар о левую стену
                    player_physics.position.x = platform.rect.left - PLAYER_WIDTH
                    player_physics.velocity.x = 0
                elif min_overlap == overlap_right and player_physics.velocity.x < 0:
                    # Удар о правую стену
                    player_physics.position.x = platform.rect.right
                    player_physics.velocity.x = 0
        
        # Проверка коллизий с surf рампами
        for ramp in self.ramps:
            if ramp.check_collision(player_rect):
                player_physics.on_surf = True
                player_physics.surf_normal = ramp.normal
                
                # Простая коллизия с рампой - не даем проваливаться
                # В реальной реализации здесь была бы более сложная геометрия
                if not player_physics.on_ground:
                    player_physics.on_ground = True
                    player_physics.velocity.y = max(0, player_physics.velocity.y)
    
    def draw(self, screen, camera_offset):
        """Рисует всю карту"""
        # Рисуем платформы
        for platform in self.platforms:
            platform.draw(screen, camera_offset)
        
        # Рисуем surf рампы
        for ramp in self.ramps:
            ramp.draw(screen, camera_offset)
