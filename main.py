import pygame
import sys
import math
from config import *
from player import Player
from surf_map import SurfMap
from camera import Camera

class SurfGame:
    def __init__(self):
        pygame.init()
        
        # Настройка экрана
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("CS:GO Style Surf - Python")
        
        # Часы для FPS
        self.clock = pygame.time.Clock()
        
        # Шрифт для UI
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # Игровые объекты
        self.player = Player(100, 100)
        self.surf_map = SurfMap()
        self.camera = Camera()
        self.camera.set_target(self.player)
        
        # Управление мышью
        pygame.mouse.set_visible(False)
        pygame.event.set_grab(True)
        
        # Игровое состояние
        self.running = True
        self.paused = False
        
    def handle_events(self):
        """Обрабатывает события"""
        mouse_rel = (0, 0)
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_r:
                    # Перезапуск
                    self.player.reset_position(100, 100)
                elif event.key == pygame.K_p:
                    # Пауза
                    self.paused = not self.paused
                    if self.paused:
                        pygame.mouse.set_visible(True)
                        pygame.event.set_grab(False)
                    else:
                        pygame.mouse.set_visible(False)
                        pygame.event.set_grab(True)
            
            elif event.type == pygame.MOUSEMOTION and not self.paused:
                mouse_rel = event.rel
        
        return mouse_rel
    
    def update(self, dt, mouse_rel):
        """Обновляет игру"""
        if self.paused:
            return
            
        # Получаем состояние клавиш
        keys = pygame.key.get_pressed()
        
        # Обрабатываем ввод игрока
        mouse_delta = self.player.handle_input(keys, mouse_rel)
        
        # Обновляем игрока
        self.player.update(dt, mouse_delta)
        
        # Проверяем коллизии
        player_rect = self.player.get_rect()
        self.surf_map.check_collisions(player_rect, self.player.physics)
        
        # Обновляем камеру
        self.camera.update(dt)
        
        # Проверяем падение за карту
        if self.player.physics.position.y > 1000:
            self.player.reset_position(100, 100)
    
    def draw(self):
        """Рисует игру"""
        # Очищаем экран
        self.screen.fill(BLACK)
        
        if not self.paused:
            # Рисуем карту
            self.surf_map.draw(self.screen, self.camera.offset)
            
            # Рисуем игрока
            self.player.draw(self.screen, self.camera.offset)
        
        # Рисуем HUD
        self.player.draw_hud(self.screen, self.small_font)
        
        # Рисуем инструкции
        self.draw_instructions()
        
        if self.paused:
            # Рисуем меню паузы
            self.draw_pause_menu()
        
        # Обновляем экран
        pygame.display.flip()
    
    def draw_instructions(self):
        """Рисует инструкции по управлению"""
        instructions = [
            "WASD - Movement",
            "Mouse - Look/Turn (Air Strafing)",
            "Space - Jump",
            "R - Restart",
            "P - Pause",
            "ESC - Exit"
        ]
        
        y_offset = SCREEN_HEIGHT - len(instructions) * 25 - 10
        for i, instruction in enumerate(instructions):
            text = self.small_font.render(instruction, True, WHITE)
            self.screen.blit(text, (SCREEN_WIDTH - 250, y_offset + i * 25))
    
    def draw_pause_menu(self):
        """Рисует меню паузы"""
        # Полупрозрачный фон
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        # Текст паузы
        pause_text = self.font.render("PAUSED", True, WHITE)
        text_rect = pause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.screen.blit(pause_text, text_rect)
        
        # Инструкция
        resume_text = self.small_font.render("Press P to resume", True, WHITE)
        resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        self.screen.blit(resume_text, resume_rect)
    
    def run(self):
        """Главный игровой цикл"""
        while self.running:
            # Вычисляем delta time
            dt = self.clock.tick(FPS) / 1000.0  # конвертируем в секунды
            
            # Обрабатываем события
            mouse_rel = self.handle_events()
            
            # Обновляем игру
            self.update(dt, mouse_rel)
            
            # Рисуем
            self.draw()
        
        # Завершение
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = SurfGame()
    game.run()
