import pygame
import math
from config import *
from physics import SurfPhysics, Vector2

class Player:
    def __init__(self, x, y):
        self.physics = SurfPhysics()
        self.physics.position = Vector2(x, y)
        
        # Визуальные параметры
        self.width = PLAYER_WIDTH
        self.height = PLAYER_HEIGHT
        self.color = BLUE
        
        # Управление
        self.keys_pressed = {
            'left': False,
            'right': False,
            'jump': False
        }
        
        # Статистика
        self.max_speed_reached = 0
        self.current_speed = 0
        
    def get_rect(self):
        """Возвращает прямоугольник игрока для коллизий"""
        return pygame.Rect(
            self.physics.position.x,
            self.physics.position.y,
            self.width,
            self.height
        )
    
    def handle_input(self, keys, mouse_rel):
        """Обрабатывает ввод от клавиатуры и мыши"""
        # Обновляем состояние клавиш
        self.keys_pressed['left'] = keys[pygame.K_a]
        self.keys_pressed['right'] = keys[pygame.K_d]
        self.keys_pressed['jump'] = keys[pygame.K_SPACE]
        
        # Прыжок
        if self.keys_pressed['jump']:
            self.physics.jump()
        
        # Движение влево/вправо на земле
        if self.physics.on_ground:
            if self.keys_pressed['left']:
                self.physics.velocity.x -= STRAFE_ACCEL * (1/60)  # предполагаем 60 FPS
            if self.keys_pressed['right']:
                self.physics.velocity.x += STRAFE_ACCEL * (1/60)
        
        return mouse_rel[0] if mouse_rel else 0  # возвращаем движение мыши по X
    
    def update(self, dt, mouse_delta=0):
        """Обновляет игрока"""
        # Определяем strafe input для air strafing
        strafe_input = 0
        if self.keys_pressed['left']:
            strafe_input -= 1
        if self.keys_pressed['right']:
            strafe_input += 1
        
        # Обновляем физику
        self.physics.update(dt, mouse_delta, strafe_input)
        
        # Обновляем статистику
        self.current_speed = self.physics.velocity.magnitude()
        self.max_speed_reached = max(self.max_speed_reached, self.current_speed)
    
    def draw(self, screen, camera_offset):
        """Рисует игрока"""
        screen_x = self.physics.position.x - camera_offset.x
        screen_y = self.physics.position.y - camera_offset.y
        
        # Рисуем игрока как прямоугольник
        pygame.draw.rect(screen, self.color, 
                        (screen_x, screen_y, self.width, self.height))
        
        # Рисуем направление движения
        if self.physics.velocity.magnitude() > 10:
            vel_normalized = self.physics.velocity.normalize()
            end_x = screen_x + self.width//2 + vel_normalized.x * 30
            end_y = screen_y + self.height//2 + vel_normalized.y * 30
            
            pygame.draw.line(screen, RED, 
                           (screen_x + self.width//2, screen_y + self.height//2),
                           (end_x, end_y), 3)
    
    def draw_hud(self, screen, font):
        """Рисует HUD с информацией о скорости"""
        # Текущая скорость
        speed_text = font.render(f"Speed: {int(self.current_speed)}", True, WHITE)
        screen.blit(speed_text, (10, 10))
        
        # Максимальная скорость
        max_speed_text = font.render(f"Max Speed: {int(self.max_speed_reached)}", True, WHITE)
        screen.blit(max_speed_text, (10, 40))
        
        # Состояние
        state = "Ground" if self.physics.on_ground else "Air"
        if self.physics.on_surf:
            state += " (Surfing)"
        state_text = font.render(f"State: {state}", True, WHITE)
        screen.blit(state_text, (10, 70))
        
        # Позиция
        pos_text = font.render(f"Pos: ({int(self.physics.position.x)}, {int(self.physics.position.y)})", True, WHITE)
        screen.blit(pos_text, (10, 100))
        
        # Скорость по осям
        vel_text = font.render(f"Velocity: ({int(self.physics.velocity.x)}, {int(self.physics.velocity.y)})", True, WHITE)
        screen.blit(vel_text, (10, 130))
    
    def reset_position(self, x, y):
        """Сбрасывает позицию игрока"""
        self.physics.position = Vector2(x, y)
        self.physics.velocity = Vector2(0, 0)
        self.physics.on_ground = False
        self.physics.on_surf = False
        self.max_speed_reached = 0
