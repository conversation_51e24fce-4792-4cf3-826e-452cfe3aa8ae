# CS:GO Style Surf Game

Это реализация surf механики из CS:GO на Python с использованием Pygame.

## Особенности

- **Air Strafing** - поворачивайте в воздухе с помощью мыши и клавиш A/D для набора скорости
- **Surf Ramps** - наклонные поверхности для ускорения
- **Физика импульса** - сохранение скорости при движении
- **Реалистичная гравитация и трение**
- **HUD с отображением скорости и статистики**

## Установка

1. Убедитесь, что у вас установлен Python 3.7+
2. Установите зависимости:
```bash
pip install -r requirements.txt
```

## Запуск

```bash
python main.py
```

## Управление

- **A/D** - движение влево/вправо
- **Пробел** - прыжок
- **Мышь** - поворот (важно для air strafing)
- **R** - перезапуск
- **P** - пауза
- **ESC** - выход

## Как играть в Surf

1. **Базовое движение**: Используйте A/D для движения по земле
2. **Прыжки**: Нажимайте пробел для прыжка
3. **Air Strafing**: В воздухе:
   - Поворачивайте мышью в сторону, куда хотите повернуть
   - Одновременно зажимайте A (для поворота влево) или D (для поворота вправо)
   - НЕ зажимайте W в воздухе - это замедлит вас!
4. **Surf Ramps**: Приземляйтесь на наклонные синие поверхности и скользите по ним для набора скорости
5. **Цель**: Набрать максимальную скорость и пройти карту как можно быстрее

## Советы для новичков

- Начинайте с небольших движений мыши
- Синхронизируйте движения мыши и клавиш A/D
- Не используйте W в воздухе
- Практикуйтесь на простых рампах
- Следите за HUD - ваша скорость должна расти

## Структура проекта

- `main.py` - главный файл игры
- `player.py` - класс игрока с физикой
- `physics.py` - физический движок
- `surf_map.py` - карта с surf рампами
- `camera.py` - система камеры
- `config.py` - настройки игры

## Настройка

Вы можете изменить настройки в файле `config.py`:
- Скорость и ускорение
- Чувствительность мыши
- Размеры экрана
- Физические константы
